import React from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { Plus } from "lucide-react";
import WorkspaceProjectCard from "./WorkspaceProjectCard";

export const WorkspaceMain = () => {
  return (
    <div className="flex-1 relative z-10 bg-re">
      <main className="p-8">
        <div className="w-full mx-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
              John Workspace
            </h1>
            {/* New Project Button */}
            <Button className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>New Project</span>
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {/* Dashboard content will go here */}
            <WorkspaceProjectCard />
          </div>
        </div>
      </main>
    </div>
  );
};
