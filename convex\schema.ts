import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

// Custom auth tables with optional providerAccountId for email providers
const customAuthTables = {
  ...authTables,
  authAccounts: defineTable({
    userId: v.id("users"),
    provider: v.string(),
    providerAccountId: v.optional(v.string()), // Made optional for email providers
    emailVerified: v.optional(v.string()),
    phoneVerified: v.optional(v.string()),
    secret: v.optional(v.string()),
  })
    .index("by_user_id", ["userId"])
    .index("providerAndAccountId", ["provider", "providerAccountId"]),
};

export default defineSchema({
  ...customAuthTables,
});


