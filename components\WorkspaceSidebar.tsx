"use client";

import React from "react";
import {
  Home,
  Wallet,
  Award,
  FileText,
  Heart,
  BarChart3,
  Users,
  Settings,
  HelpCircle,
  Sun,
  Moon,
  Power,
  PanelLeftOpen,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import { cn } from "@/lib/utils";

import { useTheme } from "next-themes";
import { useAuthActions } from "@convex-dev/auth/react";
import { useRouter } from "next/navigation";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import useSystemStore from "@/store/useSystem";

// Dummy data for menu items
const menuItems = [
  {
    icon: Home,
    label: "Dashboard",
    href: "/workspace",
    isActive: true,
  },
  {
    icon: Wallet,
    label: "My wallet",
    href: "/workspace/wallet",
  },
  {
    icon: Award,
    label: "Badge's",
    href: "/workspace/badges",
  },
  {
    icon: FileText,
    label: "My reports",
    href: "/workspace/reports",
  },
  {
    icon: Heart,
    label: "Favorit List",
    href: "/workspace/favorites",
  },
  {
    icon: BarChart3,
    label: "Analytics",
    href: "/workspace/analytics",
  },
  {
    icon: Users,
    label: "Team",
    href: "/workspace/team",
  },
];



interface MenuItemProps {
  icon: React.ElementType;
  label: string;
  href: string;
  isActive?: boolean;
  badge?: string;
  isCollapsed: boolean;
}

function MenuItem({
  icon: Icon,
  label,
  href,
  isActive = false,
  badge,
  isCollapsed,
}: MenuItemProps) {
  return (
    <li>
      <a
        href={href}
        className={cn(
          "flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group relative",
          isActive
            ? "bg-slate-700 dark:bg-slate-600 text-white"
            : "text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-white hover:bg-gray-200 dark:hover:bg-slate-600",
        )}
        title={isCollapsed ? label : undefined}
      >
        <div className="flex items-center space-x-3">
          <Icon className="w-4 h-4 flex-shrink-0 transition-colors duration-200" />
          {!isCollapsed && <span className="truncate">{label}</span>}
        </div>
        {badge && !isCollapsed && (
          <span className="bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full min-w-[20px] text-center flex-shrink-0">
            {badge}
          </span>
        )}
        {badge && isCollapsed && (
          <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
            {badge}
          </span>
        )}
      </a>
    </li>
  );
}

function WorkspaceSidebar() {
  const { isSidebarcollapsed, setIsSidebarcollapsed } = useSystemStore();
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const { signOut } = useAuthActions();

  const user = useQuery(api.users.currentUser);

  const toggleSidebar = () => {
    setIsSidebarcollapsed(!isSidebarcollapsed);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.push("/signin");
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div
      className={cn(
        "h-screen bg-slate-100 dark:bg-slate-900 flex flex-col transition-all duration-300 ease-in-out relative font-sans rounded-tr-lg rounded-br-lg  ",
        isSidebarcollapsed ? "w-16" : "w-64",
      )}
    >
      {/* Header */}
      <div className="p-2 ">
        <div
          className={`flex items-center ${isSidebarcollapsed ? "justify-center" : "justify-end"}`}
        >
          <div className="flex items-center space-x-3">
            <div className="text-center w-8 h-9 rounded-lg flex items-center justify-center flex-shrink-0">
              <PanelLeftOpen
                onClick={toggleSidebar}
                className={`w-5 h-5 hover:w-6 hover:h-6 text-gray-500 dark:text-gray-400 
                hover:text-gray-700 dark:hover:text-white cursor-pointer transition-all duration-200 
                ease-in-out ${isSidebarcollapsed ? "" : "rotate-180"}`}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 py-4 overflow-y-auto overflow-x-hidden">
        <ul className="space-y-1 px-3">
          {menuItems.map((item, index) => (
            <MenuItem
              key={index}
              icon={item.icon}
              label={item.label}
              href={item.href}
              isActive={item.isActive}
              isCollapsed={isSidebarcollapsed}
            />
          ))}
        </ul>
      </nav>

      {/* Footer */}
      <div className="">
        {/* Settings and Help Icons */}
        <div
          className={cn(
            "flex py-4",
            isSidebarcollapsed
              ? "flex-col space-y-2 items-center"
              : "justify-center space-x-4",
          )}
        >
          <button
            className="p-2 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-white hover:bg-gray-200 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200"
            title="Theme Toggle"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          >
            {theme == "dark" ? (
              <Sun className="w-5 h-5" />
            ) : (
              <Moon className="w-5 h-5" />
            )}
          </button>
          <button
            className="p-2 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-white hover:bg-gray-200 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200"
            title="Help"
          >
            <HelpCircle className="w-5 h-5" />
          </button>

          <button
            className="p-2 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-white hover:bg-gray-200 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200"
            title="Settings"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>

        {/* User Profile */}
        <div className="p-4 ">
          <div
            className={cn(
              "flex items-center",
              isSidebarcollapsed ? "justify-center" : "space-x-3",
            )}
          >
            <Avatar className="w-10 h-10 flex-shrink-0">
              <AvatarImage src={user?.image} alt={user?.name || "User"} />
              <AvatarFallback className="bg-slate-600 text-white text-sm">
                {user?.name?.charAt(0)?.toUpperCase() || "U"}
              </AvatarFallback>
            </Avatar>
            {!isSidebarcollapsed && (
              <>
                <div className="flex-1 min-w-0">
                  <p className="text-white text-sm font-medium truncate">
                    {user?.name || "User"}
                  </p>
                  <p className="text-slate-400 text-xs truncate">
                    {user?.email || "No email"}
                  </p>
                </div>
                <button
                  onClick={handleLogout}
                  className="text-slate-400 hover:text-white transition-colors duration-200 flex-shrink-0 cursor-pointer"
                >
                  <Power className="w-4 h-4 text-red-500" />
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default WorkspaceSidebar;
