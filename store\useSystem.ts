import { create } from "zustand";

interface AuthStore {
  notificationTabOpen: boolean;
  setNotificationTabOpen: (open: boolean) => void;

  isSidebarcollapsed: boolean;
  setIsSidebarcollapsed: (collapsed: boolean) => void;
}

const useSystemStore = create<AuthStore>((set) => ({
  notificationTabOpen: false,
  setNotificationTabOpen: (open) => set({ notificationTabOpen: open }),
  isSidebarcollapsed: true,
  setIsSidebarcollapsed: (collapsed) => set({ isSidebarcollapsed: collapsed }),
}));

export default useSystemStore;
