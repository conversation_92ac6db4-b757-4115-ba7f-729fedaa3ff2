import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Bell, Search } from "lucide-react";

import useSystemStore from "@/store/useSystem";

export const WorkspaceNavbar = () => {
  const { notificationTabOpen, setNotificationTabOpen } = useSystemStore();

  const handleNotificationClick = () => {
    console.log("Notification Clicked");
    setNotificationTabOpen(!notificationTabOpen);
  };

  return (
    <div className="bg-slate-100 dark:bg-slate-900  px-6 py-2">
      <div className="flex items-center justify-between">
        <div className="w-full flex items-center justify-between">
          {/* Search Bar */}
          <div className="relative ">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search projects..."
              className="pl-10 max-w-64 min-w-1"
            />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative">
            <Button
              variant={"ghost"}
              className="cursor-pointer"
              onClick={handleNotificationClick}
            >
              <Bell className="h-5 w-5 text-slate-400" />
            </Button>
            <div className="absolute top-2 right-3 bg-red-600 w-2 h-2 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  );
};
