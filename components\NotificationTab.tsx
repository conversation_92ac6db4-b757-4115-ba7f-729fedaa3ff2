import { Settings2, X } from "lucide-react";
import React from "react";
import { But<PERSON> } from "./ui/button";

export const NotificationTab = ({
  closeNotificationTab,
  isOpen,
}: {
  closeNotificationTab: () => void;
  isOpen: boolean;
}) => {
  return (
    <div 
      className={`absolute top-0 right-0 w-96 h-full bg-slate-900 transition-transform ease-in-out duration-300 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}
    >
      <div className="px-4 py-2 border-slate-800 border-b flex justify-between items-center">
        <p className="text-sm text-white">Notifications</p>

        <div className="flex">
          <Button
            variant={"ghost"}
            className="cursor-pointer w-8 h-8 text-slate-400 hover:text-white"
          >
            <Settings2 className="w-4 h-4" />
          </Button>

          <Button
            variant={"ghost"}
            className="cursor-pointer w-8 h-8 text-slate-400 hover:text-white"
            onClick={closeNotificationTab}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>
      <div className="px-2 py-2 border-slate-800 border-b  flex justify-between items-center">
        <div className="flex space-x-4">
          <Button
            variant={"ghost"}
            className="cursor-pointer w-3 h3 text-slate-600"
          >
            <p>All</p>
          </Button>
          <Button
            variant={"ghost"}
            className="cursor-pointer w-3 h3 text-slate-600"
          >
            <p>Unread</p>
          </Button>
        </div>
        <div>
          <Button
            variant={"ghost"}
            className="cursor-pointer w-auto h3 text-slate-600"
          >
            <p>Mark all as read</p>
          </Button>
        </div>
      </div>
    </div>
  );
};
